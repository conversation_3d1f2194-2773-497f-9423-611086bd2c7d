from fastapi import APIRouter, Request, Depends

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
import apps.models.prod_count as PCModel
import apps.services.prod_count as ProdCountService

router = APIRouter(prefix="/prod_count", tags=["生产计数"])

@router.post("/events")
@unified_resp
async def report_event(_: Request, data: PCModel.ProdCountEvent):
    """事件上报"""
    return await ProdCountService.report_event(data)

@router.get("/config/{vehicle_id}")
@unified_resp
async def get_vehicle_config(_: Request, vehicle_id: ObjectIdStr):
    """获取车辆配置"""
    return await ProdCountService.get_vehicle_config(vehicle_id)


@router.put("/config/{vehicle_id}")
@unified_resp
async def set_vehicle_config(_: Request, vehicle_id: ObjectIdStr, data: PCModel.ProdCountConfig):
    """车辆配置"""
    return await ProdCountService.set_vehicle_config(vehicle_id, data)


@router.get("/history_load", response_model=PCModel.HistoryLoadOut)
@unified_resp
async def get_history_load(_: Request, q: PCModel.Query = Depends()):
    """历史装车记录"""
    return await ProdCountService.get_history_load(q)


@router.get("/recent_day_team/{vehicle_id}")
@unified_resp
async def get_recent_team(_: Request, vehicle_id: ObjectIdStr):
    """最近班组记录"""
    return await ProdCountService.get_recent_team(vehicle_id)


@router.get("/history_team")
@unified_resp
async def get_history_team(_: Request, q: PCModel.HistoryTeamQuery = Depends()):
    """历史班组记录"""
    return await ProdCountService.get_history_team(q)


@router.get("/stats/daily")
@unified_resp
async def get_daily_stats(_: Request, date: str, vehicle_id: ObjectIdStr):
    """按日统计"""
    return await ProdCountService.get_daily_stats(date, vehicle_id)


@router.get("/stats/monthly")
@unified_resp
async def get_monthly_stats(_: Request, month: str, vehicle_id: ObjectIdStr):
    """按月统计"""
    return await ProdCountService.get_monthly_stats(month, vehicle_id)
